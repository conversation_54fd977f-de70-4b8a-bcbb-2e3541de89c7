import {
    calculateGoalPermissions,
    getUserRole,
    isGoalInTodoStatus,
    UserRole,
    PermissionContext,
    canUserEditGoal,
    canUserEditDescription,
    canUserEditType,
    canUserEditProgress
} from './goalPermissions';
import { GoalData } from '../types';

// Mock data
const mockGoalData: GoalData = {
    goalId: 'GL-1',
    id: 1,
    description: 'Test goal',
    typeId: 1,
    createdBy: 123,
    assignedTo: 456,
    progressId: 1,
    targetDate: Date.now(),
    progressName: 'To Do'
};

const mockGoalStatusOptions = {
    withoutAll: [
        { value: 1, label: 'To Do' },
        { value: 2, label: 'In Progress' },
        { value: 3, label: 'Completed' },
        { value: 4, label: 'Blocked' }
    ]
};

describe('goalPermissions', () => {
    describe('getUserRole', () => {
        it('should return CREATOR_AND_OWNER when user is both creator and owner', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 123 };
            expect(getUserRole(goal, 123)).toBe(UserRole.CREATOR_AND_OWNER);
        });

        it('should return CREATOR_ONLY when user is only creator', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 456 };
            expect(getUserRole(goal, 123)).toBe(UserRole.CREATOR_ONLY);
        });

        it('should return OWNER_ONLY when user is only owner', () => {
            const goal = { ...mockGoalData, createdBy: 456, assignedTo: 123 };
            expect(getUserRole(goal, 123)).toBe(UserRole.OWNER_ONLY);
        });

        it('should return NEITHER when user is neither creator nor owner', () => {
            const goal = { ...mockGoalData, createdBy: 456, assignedTo: 789 };
            expect(getUserRole(goal, 123)).toBe(UserRole.NEITHER);
        });

        it('should return NEITHER when goal is null', () => {
            expect(getUserRole(null, 123)).toBe(UserRole.NEITHER);
        });
    });

    describe('isGoalInTodoStatus', () => {
        it('should return true when goal is in TODO status', () => {
            expect(isGoalInTodoStatus(1, mockGoalStatusOptions)).toBe(true);
        });

        it('should return false when goal is not in TODO status', () => {
            expect(isGoalInTodoStatus(2, mockGoalStatusOptions)).toBe(false);
            expect(isGoalInTodoStatus(3, mockGoalStatusOptions)).toBe(false);
            expect(isGoalInTodoStatus(4, mockGoalStatusOptions)).toBe(false);
        });
    });

    describe('calculateGoalPermissions', () => {
        const baseContext: PermissionContext = {
            action: 'Edit',
            currentGoal: mockGoalData,
            currentUserId: 123,
            isSelfGoal: false,
            goalProgress: 1,
            goalStatusOptions: mockGoalStatusOptions,
            isDirty: true
        };

        it('should return correct permissions for Add action', () => {
            const context = { ...baseContext, action: 'Add' as const, isSelfGoal: true };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: true,
                canEditType: true,
                canEditOwner: true,
                canEditProgress: false,
                canSubmit: true
            });
        });

        it('should return no permissions when currentGoal is null', () => {
            const context = { ...baseContext, currentGoal: null };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: false,
                canEditType: false,
                canEditOwner: false,
                canEditProgress: false,
                canSubmit: false
            });
        });

        it('should return correct permissions for creator only (TODO status)', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 456 };
            const context = { ...baseContext, currentGoal: goal, goalProgress: 1 };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: true, // Can edit description in TODO
                canEditType: true, // Can edit type in any status
                canEditOwner: false,
                canEditProgress: false,
                canSubmit: true
            });
        });

        it('should return correct permissions for creator only (non-TODO status)', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 456 };
            const context = { ...baseContext, currentGoal: goal, goalProgress: 2 };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: false, // Cannot edit description in non-TODO
                canEditType: true, // Can edit type in any status
                canEditOwner: false,
                canEditProgress: false,
                canSubmit: false
            });
        });

        it('should return correct permissions for owner only', () => {
            const goal = { ...mockGoalData, createdBy: 456, assignedTo: 123 };
            const context = { ...baseContext, currentGoal: goal };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: false,
                canEditType: true, // Can edit type in any status
                canEditOwner: false,
                canEditProgress: true,
                canSubmit: true
            });
        });

        it('should return correct permissions for creator and owner (TODO status)', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 123 };
            const context = { ...baseContext, currentGoal: goal, goalProgress: 1 };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: true, // Can edit description in TODO
                canEditType: true, // Can edit type in any status
                canEditOwner: false,
                canEditProgress: true,
                canSubmit: true
            });
        });

        it('should return view-only permissions for neither creator nor owner', () => {
            const goal = { ...mockGoalData, createdBy: 456, assignedTo: 789 };
            const context = { ...baseContext, currentGoal: goal };
            const permissions = calculateGoalPermissions(context);

            expect(permissions).toEqual({
                canEditDescription: false,
                canEditType: false,
                canEditOwner: false,
                canEditProgress: false,
                canSubmit: false
            });
        });

        it('should respect isDirty flag for submit permissions', () => {
            const goal = { ...mockGoalData, createdBy: 123, assignedTo: 456 };
            const context = { ...baseContext, currentGoal: goal, goalProgress: 1, isDirty: false };
            const permissions = calculateGoalPermissions(context);

            expect(permissions.canSubmit).toBe(false);
        });
    });

    describe('Permission validation helpers', () => {
        it('canUserEditGoal should return true for all roles except NEITHER', () => {
            expect(canUserEditGoal(UserRole.CREATOR_ONLY)).toBe(true);
            expect(canUserEditGoal(UserRole.OWNER_ONLY)).toBe(true);
            expect(canUserEditGoal(UserRole.CREATOR_AND_OWNER)).toBe(true);
            expect(canUserEditGoal(UserRole.NEITHER)).toBe(false);
        });

        it('canUserEditDescription should work correctly', () => {
            expect(canUserEditDescription(UserRole.CREATOR_ONLY, true)).toBe(true);
            expect(canUserEditDescription(UserRole.CREATOR_ONLY, false)).toBe(false);
            expect(canUserEditDescription(UserRole.CREATOR_AND_OWNER, true)).toBe(true);
            expect(canUserEditDescription(UserRole.CREATOR_AND_OWNER, false)).toBe(false);
            expect(canUserEditDescription(UserRole.OWNER_ONLY, true)).toBe(false);
            expect(canUserEditDescription(UserRole.NEITHER, true)).toBe(false);
        });

        it('canUserEditType should work correctly', () => {
            expect(canUserEditType(UserRole.CREATOR_ONLY)).toBe(true);
            expect(canUserEditType(UserRole.OWNER_ONLY)).toBe(true);
            expect(canUserEditType(UserRole.CREATOR_AND_OWNER)).toBe(true);
            expect(canUserEditType(UserRole.NEITHER)).toBe(false);
        });

        it('canUserEditProgress should work correctly', () => {
            expect(canUserEditProgress(UserRole.CREATOR_ONLY)).toBe(false);
            expect(canUserEditProgress(UserRole.OWNER_ONLY)).toBe(true);
            expect(canUserEditProgress(UserRole.CREATOR_AND_OWNER)).toBe(true);
            expect(canUserEditProgress(UserRole.NEITHER)).toBe(false);
        });
    });
});
