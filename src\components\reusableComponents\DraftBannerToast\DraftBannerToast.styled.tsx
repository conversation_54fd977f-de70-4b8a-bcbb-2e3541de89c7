import { Toast } from '@medly-components/core';
import styled from 'styled-components';

export const DraftBannerToast = styled(Toast)`
    position: fixed;
    top: 0rem;
    left: 65%;
    transform: translateX(-50%);
    z-index: 10;
    margin-bottom: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 0.4rem solid orange;
    width: 70rem;
    max-width: 90vw;

    div:first-child {
        align-items: center;
    }

    div:nth-child(1) {
        background-color: rgba(255, 165, 0, 0.1);
    }

    div:nth-child(2) {
        justify-content: center;
        padding: 0.8rem 1.6rem;
    }

    div:nth-child(3) {
        display: none;
    }

    svg:nth-child(1) {
        * {
            fill: orange;
        }
    }
`;
