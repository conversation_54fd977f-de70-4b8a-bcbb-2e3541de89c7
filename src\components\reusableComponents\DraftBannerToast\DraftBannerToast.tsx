import { Text } from '@medly-components/core';
import { FC } from 'react';
import { DraftBannerToast } from './DraftBannerToast.styled';

interface DraftBannerToastProps {
    message: string;
}

export const DraftBannerToastComponent: FC<DraftBannerToastProps> = ({ message }) => {
    return (
        <DraftBannerToast
            variant="info"
            hideCloseIcon={true}
            message={
                <Text textVariant="body2" textWeight="Medium">
                    {message}
                </Text>
            }
        />
    );
};
